import {
  ExclamationCircleOutlined,
  ExportOutlined,
  PlusOutlined,
  QuestionCircleOutlined,
  SaveOutlined,
  SearchOutlined,
} from "@ant-design/icons";
import { Button, Modal, Tooltip, Input, Select } from "antd";
import React, { memo, useState } from "react";
import { AiOutlineFontSize } from "react-icons/ai";
import { ImFontSize } from "react-icons/im";
import { TableGeologyBottomProps } from "./table-geology-types";

export const TableGeologyBottom = memo<TableGeologyBottomProps>(
  ({
    addNewRowFinal,
    handleSave,
    handleExport,
    handleFontSizeChange,
    loading,
    addRowLoading,
    fontSize,
    watchedRows,
    filteredRows,
    hasUnsavedChanges,
    loadingExport,
    totalRows,
    loggingSuiteIdParams,
    geologySuites,
    geotechSuites,
    handleLoggingSuiteChange,
    handleSearch,
  }) => {
    const [isHelpModalVisible, setIsHelpModalVisible] = useState(false);

    const showHelpModal = () => {
      setIsHelpModalVisible(true);
    };

    const handleHelpModalCancel = () => {
      setIsHelpModalVisible(false);
    };

    return (
      <div className="flex flex-wrap items-center justify-between gap-4 border-t border-l border-r border-gray-200 bg-gray-50 rounded-t-lg shadow-sm p-3">
        <div className="flex flex-wrap items-center gap-2">
          {/* Primary Actions */}
          <Tooltip title="Add Row">
            <Button
              type="primary"
              onClick={addNewRowFinal}
              icon={<PlusOutlined />}
              disabled={loading || addRowLoading}
              loading={addRowLoading}
              style={{ fontSize: `${fontSize}px` }}
            >
              Add Row
            </Button>
          </Tooltip>
          <Tooltip title="Save (Ctrl+S)">
            <Button
              type="primary"
              onClick={handleSave}
              loading={loading}
              icon={<SaveOutlined />}
              style={{ fontSize: `${fontSize}px` }}
            >
              Save
            </Button>
          </Tooltip>

          {/* Logging Suite Selection */}
          <div className="flex items-center gap-2">
            <Select
              allowClear
              value={loggingSuiteIdParams}
              key={loggingSuiteIdParams}
              placeholder="Choose suite"
              className="min-w-[200px]"
              options={(geologySuites ?? [])
                .map((attribute: any) => ({
                  label: attribute.name,
                  value: `geologySuites-${attribute.id}`,
                }))
                .concat(
                  (geotechSuites ?? []).map((geotechSuite: any) => ({
                    label: geotechSuite.name,
                    value: `geotechSuites-${geotechSuite.id}`,
                  }))
                )}
              onChange={handleLoggingSuiteChange}
              disabled={loading}
            />
          </div>

          {/* Suite Selection and Search Controls */}
          <div className="flex flex-wrap items-center gap-3">
            {/* Search */}
            <Input
              placeholder="Search..."
              onChange={(e) => handleSearch(e.target.value)}
              className="w-48"
              allowClear
              disabled={loading}
              prefix={<SearchOutlined className="text-gray-500" />}
            />
          </div>
        </div>

        {/* Right Side Controls */}
        <div className="flex flex-wrap items-center gap-2">
          {hasUnsavedChanges && (
            <div className="flex items-center gap-2 px-3 py-1 bg-orange-100 border border-orange-300 rounded-md">
              <ExclamationCircleOutlined className="text-orange-600" />
              <span
                className="text-orange-800 font-medium"
                style={{ fontSize: `${fontSize}px` }}
              >
                Unsaved changes
              </span>
            </div>
          )}
          <span style={{ fontSize: `${fontSize}px`, fontWeight: "bold" }}>
            {watchedRows.length} of {totalRows} rows
          </span>
          <span>|</span>

          {/* Font Size Controls */}
          <div className="flex items-center gap-1">
            <span
              className="text-gray-600"
              style={{ fontSize: `${fontSize}px` }}
            >
              Font:
            </span>
            <Tooltip title="Decrease font size">
              <Button
                size="small"
                onClick={() => handleFontSizeChange(false)}
                disabled={fontSize <= 10}
                icon={<AiOutlineFontSize style={{ fontSize: "12px" }} />}
              />
            </Tooltip>
            <span
              className="text-gray-500 px-1"
              style={{ fontSize: `${fontSize}px` }}
            >
              {fontSize}px
            </span>
            <Tooltip title="Increase font size">
              <Button
                size="small"
                onClick={() => handleFontSizeChange(true)}
                disabled={fontSize >= 20}
                icon={<ImFontSize style={{ fontSize: "14px" }} />}
              />
            </Tooltip>
          </div>

          {/* Export */}
          <Tooltip title="Export to Excel">
            <Button
              onClick={handleExport}
              icon={<ExportOutlined />}
              disabled={loading || !watchedRows.length}
              style={{ fontSize: `${fontSize}px` }}
              loading={loadingExport}
            >
              Export
            </Button>
          </Tooltip>

          {/* Status Info */}
          <div className="text-sm text-gray-600 flex items-center gap-4">
            {/* Help Button */}
            <Tooltip title="Keyboard Controls Help">
              <Button
                onClick={showHelpModal}
                icon={<QuestionCircleOutlined />}
                disabled={loading}
                style={{ fontSize: `${fontSize}px` }}
              >
                Help
              </Button>
            </Tooltip>
          </div>
        </div>

        <Modal
          title="Keyboard Controls Help"
          open={isHelpModalVisible}
          onOk={handleHelpModalCancel}
          onCancel={handleHelpModalCancel}
          footer={[
            <Button
              key="back"
              onClick={handleHelpModalCancel}
              style={{ fontSize: `${fontSize}px` }}
            >
              Close
            </Button>,
          ]}
        >
          <div className="space-y-2" style={{ fontSize: `${fontSize}px` }}>
            <p>
              <strong>Tab</strong>: Navigates to the next editable
              cell/sub-field. Adds a new row if at the end of the table.
            </p>
            <p>
              <strong>Shift+Tab</strong>: Navigates to the previous editable
              cell/sub-field.
            </p>
            <p>
              <strong>Ctrl+Enter</strong>: Adds a new row.
            </p>
            <p>
              <strong>Ctrl+D</strong>: Deletes the current row.
            </p>
            <p>
              <strong>Ctrl+Y</strong>: Copies the current row.
            </p>
            <p>
              <strong>Ctrl+S</strong>: Saves changes (applies both within the
              grid and globally).
            </p>
          </div>
        </Modal>
      </div>
    );
  }
);

TableGeologyBottom.displayName = "TableGeologyBottom";
