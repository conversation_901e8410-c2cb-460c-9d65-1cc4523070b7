import { useEffect, useState } from "react";
import { downholeRequest } from "../api/downhole.api";
import { TableCommon } from "@/components/common/table-common";
import { TablePaginationConfig } from "antd";

export const Desurvey = ({ id }: { id: string }) => {
  const [data, setData] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [columns, setColumns] = useState<any[]>([]);
  const [pagination, setPagination] = useState<TablePaginationConfig>({
    current: 1,
    pageSize: 10,
    total: 0,
  });

  const fetchData = async (currentPage: number = 1, pageSize: number = 10) => {
    setLoading(true);
    try {
      const skipCount = (currentPage - 1) * pageSize;
      const response: any = await downholeRequest.getDesurveyData({
        DrillHoleId: Number(id),
        skipCount: skipCount,
        maxResultCount: pageSize,
      });

      const _data = response?.result?.items || [];
      setData(_data);

      if (_data.length > 0) {
        let _columnsName = Object.keys(_data[0]);
        let result: any[] = [];
        _columnsName.forEach((d) => {
          switch (d) {
            case "id":
              break;
            case "projectId":
              break;
            case "prospectId":
              break;
            case "drillHoleId":
              break;
            default:
              result.push({
                title: <p className="capitalize">{d}</p>,
                dataIndex: d,
              });
              break;
          }
        });
        setColumns(result);
      }

      // Update pagination with total count from API response
      setPagination((prev) => ({
        ...prev,
        total: response.result?.totalCount || _data.length,
      }));
    } catch (error) {
      console.error("Error fetching desurvey data:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [id]);

  const handleTableChange = (paginationConfig: TablePaginationConfig) => {
    const { current = 1, pageSize = 10 } = paginationConfig;
    setPagination(paginationConfig);
    fetchData(current, pageSize);
  };

  return (
    <TableCommon
      columns={columns}
      dataSource={data}
      loading={loading}
      pagination={pagination}
      onChange={handleTableChange}
    />
  );
};
